import {
  RiArticleLine,
  RiCustomerService2Line,
  RiLock2Line
} from "@remixicon/react";
import { REPORTS_ROUTE_PATH } from "features/Reports/routePath";
import { ROUTE_PATH } from "routes";
import useUserStore from "stores/user";
import useFeatureFlags from "./useFeatureFlags";

const useSidebarLinks = () => {
  const userData = useUserStore((state) => state.userInfo.user);
  const { isFeatureEnabled } = useFeatureFlags();

  const sidebarLinks = [
    {
      path: userData?.is_subscription
        ? ROUTE_PATH.HOME
        : ROUTE_PATH.SUBSCRIPTIONS,
      label: "Secure Chat",
      icon: RiLock2Line,
    },
    isFeatureEnabled("REPORTS")
      ? {
          path: REPORTS_ROUTE_PATH.REPORTS,
          label: "Reports",
          icon: RiArticleLine,
        }
      : null,
    {
      path: ROUTE_PATH.CONTACT_US,
      label: "Contact Us",
      icon: RiCustomerService2Line,
    },
  ].filter(Boolean);
  return [sidebarLinks];
};

export default useSidebarLinks;
