import { LoadingOverlay } from "components";
import {
  useFeatureFlags,
  useOrganisationEnable,
  useRouteRedirection,
  useUserRoles,
} from "hooks";
import useIsOfflineAccount from "hooks/useIsOfflineAccount";
import { AuthLayout, MainLayout, PublicLayout } from "layouts";
import {
  Acknowledge,
  ContactUs,
  NotFound,
  Payment,
  PaymentStatus,
  Subscriptions,
  VerifyMember,
} from "pages";
import { useState } from "react";
import { Navigate, Route, Routes } from "react-router-dom";
import { PolicyRoutes, PrivateRoutes, PublicRoutes, ROUTE_PATH } from "routes";
import useUserStore from "stores/user";
import useUtilStore from "stores/util";
import { VerifyAccountTypeEnum } from "../types";

export const RoutingProvider = () => {
  const userInfo = useUserStore((state) => state.userInfo);
  const isOrganisationEnabled = useOrganisationEnable();
  const IsOfflineAccount = useIsOfflineAccount();
  const isLoggingOut = useUtilStore((state) => state.isLoggingOut);
  const { token, user } = userInfo;
  const [loading, setLoading] = useState(true);
  const userRole = useUserRoles();
  const { isFeatureEnabled } = useFeatureFlags();

  useRouteRedirection(userInfo, setLoading);

  if (isLoggingOut || loading) {
    return <LoadingOverlay show={true} />;
  }

  return (
    <Routes>
      {token && user?.email_verified ? (
        <>
          {token && user?.email_verified && !user?.is_subscription ? (
            <Route element={<MainLayout />}>
              <Route
                path={ROUTE_PATH.SUBSCRIPTIONS}
                element={<Subscriptions />}
              />
              <Route path={ROUTE_PATH.PAYMENT} element={<Payment />} />
              <Route
                path={ROUTE_PATH.PAYMENT_STATUS}
                element={<PaymentStatus />}
              />
              <Route path={ROUTE_PATH.CONTACT_US} element={<ContactUs />} />
            </Route>
          ) : (
            <Route>
              {PrivateRoutes({
                userRole,
                isOrganisationEnabled,
                IsOfflineAccount,
                isFeatureEnabled,
              })}
            </Route>
          )}
        </>
      ) : (
        <Route element={<PublicLayout />}>{PublicRoutes()}</Route>
      )}

      <Route element={<AuthLayout />}>
        <Route
          path="*"
          element={
            token && !user?.is_subscription ? (
              <Navigate to={ROUTE_PATH.SUBSCRIPTIONS} />
            ) : (
              <NotFound />
            )
          }
        />
      </Route>
      <Route element={<AuthLayout />}>
        <Route
          path={ROUTE_PATH.VERIFY_MEMBER}
          element={<VerifyMember type={VerifyAccountTypeEnum.MEMBER} />}
        />
        <Route
          path={ROUTE_PATH.VERIFY_OFFLINE_ACCOUNT}
          element={<VerifyMember type={VerifyAccountTypeEnum.OFFLINE} />}
        />
        <Route
          path={ROUTE_PATH.ACKNOWLEDGE_OFFLINE_ACCOUNT}
          element={<Acknowledge />}
        />
      </Route>
      {PolicyRoutes()}
    </Routes>
  );
};
