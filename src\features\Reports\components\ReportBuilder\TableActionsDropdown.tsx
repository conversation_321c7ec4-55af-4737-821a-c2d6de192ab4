import {
  RiAddLine,
  Ri<PERSON>eleteBinLine,
  RiMore2Fill,
} from "@remixicon/react";
import { Editor } from "@tiptap/react";
import { useEffect, useRef, useState } from "react";
import { Button, Dropdown } from "react-bootstrap";

interface TableActionsDropdownProps {
  editor: Editor | null;
}

const TableActionsDropdown = ({ editor }: TableActionsDropdownProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const [hoveredTable, setHoveredTable] = useState<HTMLElement | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (!editor) return;

    const handleMouseMove = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const table = target.closest('table');

      if (table && table !== hoveredTable) {
        // Clear any existing timeout
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }

        // Remove hover class from previous table
        if (hoveredTable) {
          hoveredTable.classList.remove('table-hovered');
        }

        setHoveredTable(table);

        // Add hover class to current table
        table.classList.add('table-hovered');

        // Position dropdown at top-right of table with better positioning
        const tableRect = table.getBoundingClientRect();
        const editorRect = editor.view.dom.getBoundingClientRect();
        const scrollTop = editor.view.dom.scrollTop;

        setPosition({
          top: tableRect.top - editorRect.top + scrollTop - 5,
          left: tableRect.right - editorRect.left + 5,
        });

        setIsVisible(true);
      } else if (!table && hoveredTable) {
        // Check if mouse is over dropdown
        const dropdown = dropdownRef.current;
        if (dropdown && dropdown.contains(event.target as Node)) {
          return; // Don't hide if mouse is over dropdown
        }

        // Delay hiding to allow moving to dropdown
        timeoutRef.current = setTimeout(() => {
          if (hoveredTable) {
            hoveredTable.classList.remove('table-hovered');
          }
          setIsVisible(false);
          setHoveredTable(null);
        }, 200);
      }
    };

    const handleMouseLeave = () => {
      timeoutRef.current = setTimeout(() => {
        if (hoveredTable) {
          hoveredTable.classList.remove('table-hovered');
        }
        setIsVisible(false);
        setHoveredTable(null);
      }, 300);
    };

    const editorElement = editor.view.dom;
    editorElement.addEventListener('mousemove', handleMouseMove);
    editorElement.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      editorElement.removeEventListener('mousemove', handleMouseMove);
      editorElement.removeEventListener('mouseleave', handleMouseLeave);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [editor, hoveredTable]);

  const handleDropdownMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  const handleDropdownMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      if (hoveredTable) {
        hoveredTable.classList.remove('table-hovered');
      }
      setIsVisible(false);
      setHoveredTable(null);
    }, 100);
  };

  const executeCommand = (command: () => boolean) => {
    if (!editor || !hoveredTable) return false;

    // Find a cell in the hovered table and focus it
    const firstCell = hoveredTable.querySelector('td, th');
    if (firstCell) {
      // Get the position of the cell in the editor
      const pos = editor.view.posAtDOM(firstCell, 0);
      if (pos !== null) {
        // Set selection to the cell
        editor.view.dispatch(
          editor.view.state.tr.setSelection(
            editor.view.state.selection.constructor.near(
              editor.view.state.doc.resolve(pos)
            )
          )
        );
      }
    }

    // Focus the editor
    editor.view.focus();

    // Execute the command
    const result = command();

    // Hide dropdown after execution
    setIsVisible(false);
    setHoveredTable(null);

    return result;
  };

  const isTableActive = () => {
    return editor?.isActive('table') || false;
  };

  const canDeleteRow = () => {
    // Check if there's more than one row
    const { state } = editor!;
    const { selection } = state;
    const { $from } = selection;

    // Find the table node
    let tableNode = null;
    for (let i = $from.depth; i > 0; i--) {
      if ($from.node(i).type.name === 'table') {
        tableNode = $from.node(i);
        break;
      }
    }

    return tableNode ? tableNode.childCount > 1 : false;
  };

  const canDeleteColumn = () => {
    // Check if there's more than one column
    const { state } = editor!;
    const { selection } = state;
    const { $from } = selection;

    // Find the first row to count columns
    let firstRow = null;
    for (let i = $from.depth; i > 0; i--) {
      if ($from.node(i).type.name === 'table') {
        firstRow = $from.node(i).child(0);
        break;
      }
    }

    return firstRow ? firstRow.childCount > 1 : false;
  };

  if (!isVisible || !editor) {
    return null;
  }

  return (
    <div
      ref={dropdownRef}
      className="table-actions-dropdown"
      style={{
        position: 'absolute',
        top: position.top,
        left: position.left,
        zIndex: 1000,
      }}
      onMouseEnter={handleDropdownMouseEnter}
      onMouseLeave={handleDropdownMouseLeave}
    >
      <Dropdown>
        <Dropdown.Toggle
          as={Button}
          variant=""
          className="table-actions-btn"
          // id="table-actions-dropdown"
        >
          <RiMore2Fill size={16} />
        </Dropdown.Toggle>

        <Dropdown.Menu className="table-actions-menu">
          <Dropdown.Header className="text-muted small">Add Row</Dropdown.Header>
          <Dropdown.Item
            onClick={() => executeCommand(() => editor.chain().focus().addRowBefore().run())}
            className="table-action-item"
          >
            <RiAddLine size={14} className="me-2 text-success" />
            Add Row Before
          </Dropdown.Item>
          <Dropdown.Item
            onClick={() => executeCommand(() => editor.chain().focus().addRowAfter().run())}
            className="table-action-item"
          >
            <RiAddLine size={14} className="me-2 text-success" />
            Add Row After
          </Dropdown.Item>

          <Dropdown.Divider />

          <Dropdown.Header className="text-muted small">Add Column</Dropdown.Header>
          <Dropdown.Item
            onClick={() => executeCommand(() => editor.chain().focus().addColumnBefore().run())}
            className="table-action-item"
          >
            <RiAddLine size={14} className="me-2 text-success" />
            Add Column Before
          </Dropdown.Item>
          <Dropdown.Item
            onClick={() => executeCommand(() => editor.chain().focus().addColumnAfter().run())}
            className="table-action-item"
          >
            <RiAddLine size={14} className="me-2 text-success" />
            Add Column After
          </Dropdown.Item>

          <Dropdown.Divider />

          <Dropdown.Header className="text-muted small">Delete</Dropdown.Header>
          <Dropdown.Item
            onClick={() => executeCommand(() => editor.chain().focus().deleteRow().run())}
            className={`table-action-item ${!canDeleteRow() ? 'disabled' : 'text-danger'}`}
            disabled={!canDeleteRow()}
          >
            <RiDeleteBinLine size={14} className="me-2" />
            Delete Row
          </Dropdown.Item>
          <Dropdown.Item
            onClick={() => executeCommand(() => editor.chain().focus().deleteColumn().run())}
            className={`table-action-item ${!canDeleteColumn() ? 'disabled' : 'text-danger'}`}
            disabled={!canDeleteColumn()}
          >
            <RiDeleteBinLine size={14} className="me-2" />
            Delete Column
          </Dropdown.Item>
          <Dropdown.Item
            onClick={() => executeCommand(() => editor.chain().focus().deleteTable().run())}
            className="table-action-item text-danger"
          >
            <RiDeleteBinLine size={14} className="me-2" />
            Delete Table
          </Dropdown.Item>
        </Dropdown.Menu>
      </Dropdown>
    </div>
  );
};

export default TableActionsDropdown;
