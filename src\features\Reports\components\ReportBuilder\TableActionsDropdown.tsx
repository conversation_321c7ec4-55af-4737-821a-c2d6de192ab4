import {
  Ri<PERSON>dd<PERSON><PERSON>,
  RiDeleteBinLine,
  Ri<PERSON>ore2Fill,
} from "@remixicon/react";
import { Editor } from "@tiptap/react";
import { useEffect, useState } from "react";
import { Button, Dropdown } from "react-bootstrap";

interface TableActionsDropdownProps {
  editor: Editor | null;
}

interface TableInfo {
  element: HTMLElement;
  position: { top: number; left: number };
}

const TableActionsDropdown = ({ editor }: TableActionsDropdownProps) => {
  const [tableInfos, setTableInfos] = useState<TableInfo[]>([]);

  useEffect(() => {
    if (!editor) return;

    const updateTables = () => {
      const editorElement = editor.view.dom;
      const tableElements = Array.from(editorElement.querySelectorAll('table')) as HTMLElement[];
      const editorRect = editorElement.getBoundingClientRect();

      const infos: TableInfo[] = tableElements.map(table => {
        const tableRect = table.getBoundingClientRect();
        return {
          element: table,
          position: {
            top: tableRect.top - editorRect.top,
            left: tableRect.right - editorRect.left + 5,
          }
        };
      });

      setTableInfos(infos);
    };

    // Update tables on editor content change
    editor.on('update', updateTables);
    editor.on('transaction', updateTables);

    // Initial update
    setTimeout(updateTables, 100);

    return () => {
      editor.off('update', updateTables);
      editor.off('transaction', updateTables);
    };
  }, [editor]);

  const executeCommand = (command: () => boolean, table: HTMLElement) => {
    if (!editor) return false;

    // Find a cell in the table and focus it
    const firstCell = table.querySelector('td, th');
    if (firstCell) {
      // Get the position of the cell in the editor
      const pos = editor.view.posAtDOM(firstCell, 0);
      if (pos !== null) {
        // Set selection to the cell
        const tr = editor.view.state.tr.setSelection(
          editor.view.state.selection.constructor.near(
            editor.view.state.doc.resolve(pos)
          )
        );
        editor.view.dispatch(tr);
      }
    }

    // Focus the editor
    editor.view.focus();

    // Execute the command
    const result = command();

    return result;
  };

  const canDeleteRow = (table: HTMLElement) => {
    const rows = table.querySelectorAll('tr');
    return rows.length > 1;
  };

  const canDeleteColumn = (table: HTMLElement) => {
    const firstRow = table.querySelector('tr');
    if (!firstRow) return false;
    const cells = firstRow.querySelectorAll('td, th');
    return cells.length > 1;
  };

  if (!editor) {
    return null;
  }

  return (
    <>
      {tableInfos.map((tableInfo, index) => (
        <div
          key={`table-${index}`}
          className="table-actions-dropdown"
          style={{
            position: 'absolute',
            top: tableInfo.position.top,
            left: tableInfo.position.left,
            zIndex: 1000,
          }}
        >
          <Dropdown>
            <Dropdown.Toggle
              as={Button}
              variant=""
              className="table-actions-btn"
              id={`table-actions-dropdown-${index}`}
            >
              <RiMore2Fill size={16} />
            </Dropdown.Toggle>

            <Dropdown.Menu className="table-actions-menu">
              <Dropdown.Header className="text-muted small">Add Row</Dropdown.Header>
              <Dropdown.Item
                onClick={() => executeCommand(() => editor.chain().focus().addRowBefore().run(), tableInfo.element)}
                className="table-action-item"
              >
                <RiAddLine size={14} className="me-2 text-success" />
                Add Row Before
              </Dropdown.Item>
              <Dropdown.Item
                onClick={() => executeCommand(() => editor.chain().focus().addRowAfter().run(), tableInfo.element)}
                className="table-action-item"
              >
                <RiAddLine size={14} className="me-2 text-success" />
                Add Row After
              </Dropdown.Item>

              <Dropdown.Divider />

              <Dropdown.Header className="text-muted small">Add Column</Dropdown.Header>
              <Dropdown.Item
                onClick={() => executeCommand(() => editor.chain().focus().addColumnBefore().run(), tableInfo.element)}
                className="table-action-item"
              >
                <RiAddLine size={14} className="me-2 text-success" />
                Add Column Before
              </Dropdown.Item>
              <Dropdown.Item
                onClick={() => executeCommand(() => editor.chain().focus().addColumnAfter().run(), tableInfo.element)}
                className="table-action-item"
              >
                <RiAddLine size={14} className="me-2 text-success" />
                Add Column After
              </Dropdown.Item>

              <Dropdown.Divider />

              <Dropdown.Header className="text-muted small">Delete</Dropdown.Header>
              <Dropdown.Item
                onClick={() => executeCommand(() => editor.chain().focus().deleteRow().run(), tableInfo.element)}
                className={`table-action-item ${!canDeleteRow(tableInfo.element) ? 'disabled' : 'text-danger'}`}
                disabled={!canDeleteRow(tableInfo.element)}
              >
                <RiDeleteBinLine size={14} className="me-2" />
                Delete Row
              </Dropdown.Item>
              <Dropdown.Item
                onClick={() => executeCommand(() => editor.chain().focus().deleteColumn().run(), tableInfo.element)}
                className={`table-action-item ${!canDeleteColumn(tableInfo.element) ? 'disabled' : 'text-danger'}`}
                disabled={!canDeleteColumn(tableInfo.element)}
              >
                <RiDeleteBinLine size={14} className="me-2" />
                Delete Column
              </Dropdown.Item>
              <Dropdown.Item
                onClick={() => executeCommand(() => editor.chain().focus().deleteTable().run(), tableInfo.element)}
                className="table-action-item text-danger"
              >
                <RiDeleteBinLine size={14} className="me-2" />
                Delete Table
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
        </div>
      ))}
    </>
  );
};

export default TableActionsDropdown;
