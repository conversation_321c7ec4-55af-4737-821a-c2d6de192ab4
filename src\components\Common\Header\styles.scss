@use "/src/styles/mixins/mixins.scss" as mixins;

.header {
  &-nav {
    gap: 30px;

    &-left {
      position: relative;
      z-index: 0;
      flex: 1;
      padding: 24px 24px 24px 32px;
      border-radius: 12px;
      background: #ffffff;
      box-shadow:
        0px 100px 80px 0px rgba(26, 26, 26, 0.05),
        0px 64.815px 46.852px 0px rgba(26, 26, 26, 0.04),
        0px 38.519px 25.481px 0px rgba(26, 26, 26, 0.03),
        0px 20px 13px 0px rgba(26, 26, 26, 0.03),
        0px 8.148px 6.519px 0px rgba(26, 26, 26, 0.02),
        0px 1.852px 3.148px 0px rgba(26, 26, 26, 0.01);

      .page-title {
        font-weight: 700;
      }

      @media only screen and (max-width: 576px) {
        padding: 15px;
      }

      @include mixins.notification-dot(5px, 5px);
    }

    &-right {
      padding: 24px 24px 24px 24px;
      border-radius: 12px;
      gap: 30px;
      box-shadow:
        0px 100px 80px 0px rgba(26, 26, 26, 0.05),
        0px 64.815px 46.852px 0px rgba(26, 26, 26, 0.04),
        0px 38.519px 25.481px 0px rgba(26, 26, 26, 0.03),
        0px 20px 13px 0px rgba(26, 26, 26, 0.03),
        0px 8.148px 6.519px 0px rgba(26, 26, 26, 0.02),
        0px 1.852px 3.148px 0px rgba(26, 26, 26, 0.01);

      .profile-data {
        gap: 5px;

        &-img {
          width: 45px;
          height: 45px;
        }

        &-name {
          font-size: 14px;
        }
      }

      .nav-link {
        @include mixins.header-nav-link-base;

        .form {
          &-check-input {
            margin: 0;
          }

          &-switch {
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }

        @include mixins.notification-dot(8px, 6px);
      }
    }
  }
  .info-bar {
    background-color: #fff3cd;
    color: #856404;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    border-bottom: 1px solid #ffeeba;
    border-radius: 5px;
  }
}
