import { RiGroup2Fill } from "@remixicon/react";
import {
  useCurrentUpcomingSubscription,
  useGetOrganisation,
  useGetOrganisationMembers,
} from "api";
import { OrgUserStatus, STATUS_LABEL } from "globals";
import { useDebounce, useSameCurrentUser, useUserStatus } from "hooks";
import { useEffect, useState } from "react";
import { setMyPlan } from "stores";
import {
  getOrganisationInfo,
  setOrganisation,
  setOrganisationMember,
} from "stores/user";
import { formatNumberWithCommas } from "utils";
import ActionsCell from "./ActionCell";
import StatusDropdown from "./StatusDropdown";

const ORGANISATION_MEMBERS_QUERY_KEY = "organisation-members";

export const useOrgManagerConfig = () => {
  const [paginationConfig, setPaginationConfig] = useState<any>({
    page: 1,
    limit: 25,
  });
  const [filters, setFilters] = useState<any>({
    search: undefined,
    sort: "desc",
    sort_column: undefined,
  });
  const [openDropdownId, setOpenDropdownId] = useState<number | string | null>(
    null,
  );

  const { data: { organization: orgInfo = {}, organizationMember = {} } = {} } =
    useGetOrganisation({
      staleTime: 0,
    });
  const organization = getOrganisationInfo();
  const { isOfflineAccount } = useUserStatus();
  const checkSameUser = useSameCurrentUser();
  const debouncedQry = useDebounce(filters.search, 500);

  const {
    data: {
      data: members = [],
      count: totalCount,
      word_limit = 0,
      assigned_words = 0,
      word_limit_setting = {},
    } = {},
    isLoading,
  } = useGetOrganisationMembers({
    id: organization?.id,
    params: {
      ...paginationConfig,
      keyword: debouncedQry,
      sort_order: filters.sort?.toUpperCase(),
      sort_column: filters.sort_column,
    },
  });

  const wordCountConfig = {
    word_limit,
    assigned_words,
    word_limit_setting,
  };

  const { data: currentUpcomingSubscription = {} } =
    useCurrentUpcomingSubscription({
      options: {
        enabled: !isOfflineAccount,
      },
    });

  const handleDropdownToggle = (rowId: number | string) => {
    setOpenDropdownId((prevId) => (prevId === rowId ? null : rowId));
  };

  const columns: any = [
    {
      field: "full_name",
      headerName: "Name",
      renderCell: (row: any) => (
        <p className="mb-0 fw-bold">{row?.full_name ?? "NA"}</p>
      ),
      isSortable: true,
      sortableColumn: "full_name",
    },
    {
      field: "email",
      headerName: "Email",
      renderCell: (row: any) => (
        <p className="mb-0 fw-bold">{row?.email ?? "NA"}</p>
      ),
      isSortable: true,
      sortableColumn: "email",
    },
    {
      field: "role",
      headerName: "Permissions",
      renderCell: (row: any) => (
        <StatusDropdown
          key={row?.id}
          row={row}
          organization={organization}
          queryKeys={[ORGANISATION_MEMBERS_QUERY_KEY]}
          isOpen={openDropdownId === row?.id}
          onToggle={() => handleDropdownToggle(row?.id)}
          disabled={
            checkSameUser(row?.member_id) ||
            row?.status === OrgUserStatus.CANCEL
          }
        />
      ),
      isSortable: true,
      sortableColumn: "role",
    },
    {
      field: "status",
      headerName: "Status",
      renderCell: (row: any) => (
        <p
          className={`mb-0 rounded-pill text-center font-light fw-bold px-1 py-1 text-capitalize status-pill ${row?.status}`}
        >
          {STATUS_LABEL[row?.status]}
        </p>
      ),
    },
    {
      field: "words_used",
      headerName: "Words Used",
      renderCell: (row: any) => (
        <p
          className={`mb-0 rounded-pill text-center font-light fw-bold px-1 py-1 text-capitalize status-pill`}
        >
          {row?.words_used > 0 ? formatNumberWithCommas(row?.words_used) : 0}
        </p>
      ),
    },
    {
      field: "word_count",
      headerName: "Word Limit Allocation",
      subHeader: isOfflineAccount
        ? `${formatNumberWithCommas(wordCountConfig.assigned_words)}/${formatNumberWithCommas(wordCountConfig.word_limit)}`
        : null,
      renderCell: (row: any) => (
        <p
          className={`mb-0 rounded-pill text-center font-light fw-bold px-1 py-1 text-capitalize status-pill`}
        >
          {row?.word_limit > 0 ? formatNumberWithCommas(row?.word_limit) : 0}
        </p>
      ),
    },
    {
      field: "actions",
      headerName: "Actions",
      renderCell: (row: any) => (
        <ActionsCell
          row={row}
          organization={organization}
          checkSameUser={checkSameUser}
          wordCountConfig={wordCountConfig}
          isOfflineAccount={isOfflineAccount}
        />
      ),
    },
  ];

  const handleSort = (column: any) => {
    setFilters({
      ...filters,
      sort:
        filters.sort === "asc" && column === filters.sort_column
          ? "desc"
          : "asc",
      sort_column: column,
    });
  };

  const toolbarProps = {
    title: "My Team",
    description: "View and manage your team, plus add new team members",
    icon: RiGroup2Fill,
    filters,
    setFilters,
    organisationInfo: organization,
    isOfflineAccount,
    wordCountConfig,
  };

  const dataGridProps = {
    columns,
    rows: members,
    loading: isLoading,
    paginationProps: {
      paginationConfig,
      setPaginationConfig,
      totalCount,
    },
    sortDirection: filters.sort,
    onSort: handleSort,
    filters,
  };

  useEffect(() => {
    if (orgInfo?.id) {
      setOrganisation(orgInfo);
    }
  }, [orgInfo]);

  useEffect(() => {
    if (organizationMember?.id && orgInfo?.id) {
      setOrganisationMember({
        ...organizationMember,
        organization: orgInfo,
      });
    }
  }, [orgInfo, organizationMember]);

  useEffect(() => {
    setMyPlan(currentUpcomingSubscription);
  }, [currentUpcomingSubscription]);

  return {
    organization,
    toolbarProps,
    dataGridProps,
  };
};
