import {
    RiCloseLine,
    RiFileTextLine,
    RiSparkling2Line,
} from "@remixicon/react";
import { CustomDropdown } from "components";
import { addReportBlockAtIndex } from "features/Reports/store";
import React from "react";
import { Button, Modal } from "react-bootstrap";

interface InsertSectionModalProps {
  show: boolean;
  onClose: () => void;
  index: number;
}

const InsertSectionModal: React.FC<InsertSectionModalProps> = ({
  show,
  onClose,
  index,
}) => {
  const handleClose = () => {
    onClose();
  };

  const handleSelectItem = (value: string) => {
    console.log(value);
  };

  const handleAddSection = (type: string) => {
    if (type === "new") {
      const newSection = {
        title: "New Section",
        content: "",
        sortOrder: 0,
      };
      addReportBlockAtIndex(newSection, index);
    }
    handleClose();
  };

  return (
    <Modal
      show={show}
      onHide={handleClose}
      keyboard={false}
      centered
      className="insert-section-modal"
    >
      <Modal.Body className="d-flex justify-content-center align-items-center position-relative">
        <Button
          variant="link"
          className="text-decoration-none modal-close-button bg-brown rounded-circle position-absolute z-3 d-flex justify-content-center align-items-center"
          onClick={handleClose}
        >
          <RiCloseLine size={"40px"} color="#f9f9f9" />
        </Button>

        <div
          className="auth-form d-flex justify-content-center align-items-stretch flex-column"
          style={{ gap: "20px" }}
        >
          <div className="d-flex flex-column" style={{ gap: "15px" }}>
            <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
              Insert Section
            </h1>
          </div>

          <div className="d-flex flex-column gap-3">
            <Button
              variant=""
              className="w-100 border-blue fw-bold py-2"
              onClick={() => handleAddSection("new")}
            >
              Insert Blank Section
            </Button>
            <CustomDropdown
              title="Insert Saved Section"
              items={[
                {
                  label: (
                    <span>
                      <RiSparkling2Line size={18} className="me-2" />
                      Export With Branding
                    </span>
                  ),
                  value: "true",
                },
                {
                  label: (
                    <span>
                      <RiFileTextLine size={18} className="me-2" />
                      Export As Plain File
                    </span>
                  ),
                  value: "false",
                },
              ]}
              className="insert-saved-section"
              onSelect={handleSelectItem}
              preserveTitle
            />
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default InsertSectionModal;
