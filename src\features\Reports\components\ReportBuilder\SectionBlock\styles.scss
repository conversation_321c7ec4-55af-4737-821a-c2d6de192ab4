.section-block {
  margin-bottom: 20px;

  .section-header {
    .lock-icon {
      margin-right: 8px;
      color: #ad986f;
    }

    .title {
      .section-lock-btn {
        background: none;
        border: none;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.2s ease;
        cursor: pointer;

        &:hover {
          background-color: rgba(173, 152, 111, 0.1);
        }

        &.locked {
          background-color: rgba(173, 152, 111, 0.2);
        }

        &.partial {
          background-color: rgba(255, 193, 7, 0.2);

          svg {
            color: #ffc107 !important;
          }
        }

        &.unlocked {
          background-color: transparent;
        }
      }

      .section-title-display {
        color: #0d3149;
        font-weight: 600;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s ease;
        border: 2px solid transparent;

        &:hover {
          background-color: #f8f9fa;
          border-color: #ad986f;
          outline: 2px solid rgba(173, 152, 111, 0.2);
        }
      }

      .section-title-input {
        color: #0d3149;
        font-size: 1.25rem;
        border: 2px solid #ad986f;
        border-radius: 4px;
        padding: 4px 8px;
        background-color: #fff;
        outline: none;
        min-width: 200px;

        &:focus {
          border-color: #ad986f;
          box-shadow: 0 0 0 0.2rem rgba(173, 152, 111, 0.25);
        }
      }

      h5 {
        color: #0d3149;
        font-weight: 600;
      }
    }
  }

  .section-content {
    position: relative;

    .editor-container {
      position: relative;

      .editor-box {
        .tiptap {
          width: 100%;
          min-height: 150px;
          padding: 0.75rem;
          border-radius: 4px;
          border: 1px solid #ccc;
          background-color: #fff;
          resize: vertical;

          ::selection {
            background-color: #ad986f;
            color: #000;
          }

          ::-moz-selection {
            background-color: rgb(173, 152, 111);
            color: #000;
          }

          .content-locked {
            background-color: rgba(173, 152, 111, 0.15);
            border-radius: 3px;
            padding: 1px 3px;
            position: relative;

            &::before {
              content: "🔒";
              font-size: 0.7em;
              margin-right: 2px;
              opacity: 0.7;
            }

            &:hover {
              background-color: rgba(173, 152, 111, 0.25);
            }
          }

          table {
            border-collapse: collapse;
            margin: 0;
            overflow: hidden;
            table-layout: fixed;
            width: 100%;
            transition: all 0.2s ease;
            position: relative;

            &:hover {
              box-shadow: 0 2px 8px rgba(173, 152, 111, 0.15);
              border-radius: 4px;
            }

            td,
            th {
              border: 2px solid #ced4da;
              box-sizing: border-box;
              min-width: 1em;
              padding: 3px 5px;
              position: relative;
              vertical-align: top;

              > * {
                margin-bottom: 0;
              }
            }

            th {
              background-color: #f8f9fa;
              font-weight: bold;
            }

            .selectedCell:after {
              background: rgba(200, 200, 255, 0.4);
              content: "";
              left: 0;
              right: 0;
              top: 0;
              bottom: 0;
              pointer-events: none;
              position: absolute;
              z-index: 2;
            }

            .column-resize-handle {
              background-color: #adf;
              bottom: -2px;
              position: absolute;
              right: -2px;
              top: 0;
              width: 4px;
            }
          }

          .graph-placeholder {
            border: 2px dashed #ad986f;
            border-radius: 8px;
            padding: 2rem;
            margin: 1rem 0;
            text-align: center;
            background-color: #f8f9fa;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background-color: #e9ecef;
              border-color: #8b7355;
            }

            .graph-placeholder-content {
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 0.5rem;
            }

            .graph-placeholder-icon {
              font-size: 2rem;
            }

            .graph-placeholder-text {
              font-weight: 600;
              color: #ad986f;
            }

            .graph-placeholder-subtitle {
              font-size: 0.875rem;
              color: #6c757d;
            }
          }

          img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
          }
        }
      }

      .editor-toolbar {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .toolbar-buttons {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .toolbar-btn {
          background-color: #ad986f;
          border: none;
          color: white;
          width: 40px;
          height: 40px;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background-color: #8b7355;
            transform: translateY(-1px);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }

    .sidebar-container {
      position: absolute;
      right: -70px;
      top: 0;
      display: flex;
      flex-direction: column;
      gap: 20px;

      .save-btn-container {
        .save-btn {
          background-color: #0d3149;
          color: white;
          border: none;
          width: 45px;
          height: 45px;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
      }

      .insert-options-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        background-color: white;
        padding: 10px;
        border-radius: 4px;
        border: 1px solid #ccc;

        .insert-label {
          font-size: 0.8rem;
          color: #666;
          margin-bottom: 5px;
        }

        .insert-btn {
          background: none;
          border: none;
          cursor: pointer;
          color: #0d3149;
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            color: #ad986f;
          }
        }
      }
    }
  }
}

// Selection Lock Button Styles
.selection-lock-button {
  .lock-btn {
    background-color: #ad986f;
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

    &:hover {
      background-color: #8b7355;
      transform: scale(1.05);
    }

    // &.locked {
    //   background-color: #dc3545;

    //   &:hover {
    //     background-color: #c82333;
    //   }
    // }

    // &.unlocked {
    //   background-color: #28a745;

    //   &:hover {
    //     background-color: #218838;
    //   }
    // }
  }
}

// Table Actions Dropdown Styles
.table-actions-dropdown {
  .table-actions-btn {
    background-color: #ad986f;
    border: none;
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    padding: 0;

    &:hover {
      background-color: #8b7355;
      transform: scale(1.05);
    }

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(173, 152, 111, 0.25);
    }

    &::after {
      display: none; // Hide default dropdown arrow
    }
  }

  .table-actions-menu {
    border-radius: 8px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 8px 0;
    min-width: 200px;

    .dropdown-header {
      font-weight: 600;
      color: #6c757d;
      font-size: 0.75rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      padding: 8px 16px 4px;
    }

    .table-action-item {
      padding: 8px 16px;
      font-size: 0.875rem;
      display: flex;
      align-items: center;
      transition: all 0.2s ease;

      &:hover {
        background-color: #f8f9fa;
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
          background-color: transparent;
        }
      }

      &.text-danger:hover {
        background-color: rgba(220, 53, 69, 0.1);
      }
    }

    .dropdown-divider {
      margin: 8px 0;
      border-color: #e9ecef;
    }
  }
}

.ProseMirror > :first-child.is-empty::before {
  content: "Start Typing...";
  color: #808080;
  position: absolute;
  top: 10px;
  left: 15px;
}
