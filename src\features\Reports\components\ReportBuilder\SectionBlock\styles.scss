.section-block {
  margin-bottom: 20px;

  .section-header {
    .lock-icon {
      margin-right: 8px;
      color: #ad986f;
    }

    .title {
      .section-lock-btn {
        background: none;
        border: none;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.2s ease;
        cursor: pointer;

        &:hover {
          background-color: rgba(173, 152, 111, 0.1);
        }

        &.locked {
          background-color: rgba(173, 152, 111, 0.2);
        }

        &.partial {
          background-color: rgba(255, 193, 7, 0.2);

          svg {
            color: #ffc107 !important;
          }
        }

        &.unlocked {
          background-color: transparent;
        }
      }

      .section-title-display {
        color: #0d3149;
        font-weight: 600;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s ease;
        border: 2px solid transparent;

        &:hover {
          background-color: #f8f9fa;
          border-color: #ad986f;
          outline: 2px solid rgba(173, 152, 111, 0.2);
        }
      }

      .section-title-input {
        color: #0d3149;
        font-size: 1.25rem;
        border: 2px solid #ad986f;
        border-radius: 4px;
        padding: 4px 8px;
        background-color: #fff;
        outline: none;
        min-width: 200px;

        &:focus {
          border-color: #ad986f;
          box-shadow: 0 0 0 0.2rem rgba(173, 152, 111, 0.25);
        }
      }

      h5 {
        color: #0d3149;
        font-weight: 600;
      }
    }
  }

  .section-content {
    position: relative;

    .editor-container {
      position: relative;

      .editor-toolbar {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .toolbar-buttons {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .toolbar-btn {
          background-color: #ad986f;
          border: none;
          color: white;
          width: 40px;
          height: 40px;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background-color: #8b7355;
            transform: translateY(-1px);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }

    .sidebar-container {
      position: absolute;
      right: -70px;
      top: 0;
      display: flex;
      flex-direction: column;
      gap: 20px;

      .save-btn-container {
        .save-btn {
          background-color: #0d3149;
          color: white;
          border: none;
          width: 45px;
          height: 45px;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
      }

      .insert-options-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        background-color: white;
        padding: 10px;
        border-radius: 4px;
        border: 1px solid #ccc;

        .insert-label {
          font-size: 0.8rem;
          color: #666;
          margin-bottom: 5px;
        }

        .insert-btn {
          background: none;
          border: none;
          cursor: pointer;
          color: #0d3149;
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            color: #ad986f;
          }
        }
      }
    }
  }
}

// Selection Lock Button Styles
.selection-lock-button {
  .lock-btn {
    background-color: #ad986f;
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

    &:hover {
      background-color: #8b7355;
      transform: scale(1.05);
    }

    // &.locked {
    //   background-color: #dc3545;

    //   &:hover {
    //     background-color: #c82333;
    //   }
    // }

    // &.unlocked {
    //   background-color: #28a745;

    //   &:hover {
    //     background-color: #218838;
    //   }
    // }
  }
}

.ProseMirror > :first-child.is-empty::before {
  content: "Start Typing...";
  color: #808080;
  position: absolute;
  top: 10px;
  left: 15px;
}
