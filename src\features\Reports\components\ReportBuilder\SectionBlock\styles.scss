.section-block {
  margin-bottom: 20px;

  .section-header {
    .lock-icon {
      margin-right: 8px;
      color: #ad986f;
    }

    .title {
      .section-title-display {
        color: #0d3149;
        font-weight: 600;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s ease;
        border: 2px solid transparent;

        &:hover {
          background-color: #f8f9fa;
          border-color: #ad986f;
          outline: 2px solid rgba(173, 152, 111, 0.2);
        }
      }

      .section-title-input {
        color: #0d3149;
        font-size: 1.25rem;
        border: 2px solid #ad986f;
        border-radius: 4px;
        padding: 4px 8px;
        background-color: #fff;
        outline: none;
        min-width: 200px;

        &:focus {
          border-color: #ad986f;
          box-shadow: 0 0 0 0.2rem rgba(173, 152, 111, 0.25);
        }
      }

      h5 {
        color: #0d3149;
        font-weight: 600;
      }
    }
  }

  .section-content {
    position: relative;

    .editor-container {
      position: relative;

      .editor-box {
        .tiptap {
          width: 100%;
          min-height: 150px;
          padding: 0.75rem;
          border-radius: 4px;
          border: 1px solid #ccc;
          background-color: #fff;
          resize: vertical;

          table {
            border-collapse: collapse;
            margin: 0;
            overflow: hidden;
            table-layout: fixed;
            width: 100%;

            td,
            th {
              border: 2px solid #ced4da;
              box-sizing: border-box;
              min-width: 1em;
              padding: 3px 5px;
              position: relative;
              vertical-align: top;

              > * {
                margin-bottom: 0;
              }
            }

            th {
              background-color: #f8f9fa;
              font-weight: bold;
            }

            .selectedCell:after {
              background: rgba(200, 200, 255, 0.4);
              content: "";
              left: 0;
              right: 0;
              top: 0;
              bottom: 0;
              pointer-events: none;
              position: absolute;
              z-index: 2;
            }

            .column-resize-handle {
              background-color: #adf;
              bottom: -2px;
              position: absolute;
              right: -2px;
              top: 0;
              width: 4px;
            }
          }

          .graph-placeholder {
            border: 2px dashed #ad986f;
            border-radius: 8px;
            padding: 2rem;
            margin: 1rem 0;
            text-align: center;
            background-color: #f8f9fa;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background-color: #e9ecef;
              border-color: #8b7355;
            }

            .graph-placeholder-content {
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 0.5rem;
            }

            .graph-placeholder-icon {
              font-size: 2rem;
            }

            .graph-placeholder-text {
              font-weight: 600;
              color: #ad986f;
            }

            .graph-placeholder-subtitle {
              font-size: 0.875rem;
              color: #6c757d;
            }
          }

          img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
          }
        }
      }

      .editor-toolbar {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .toolbar-buttons {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .toolbar-btn {
          background-color: #ad986f;
          border: none;
          color: white;
          width: 40px;
          height: 40px;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background-color: #8b7355;
            transform: translateY(-1px);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }

    .sidebar-container {
      position: absolute;
      right: -70px;
      top: 0;
      display: flex;
      flex-direction: column;
      gap: 20px;

      .save-btn-container {
        .save-btn {
          background-color: #0d3149;
          color: white;
          border: none;
          width: 45px;
          height: 45px;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
      }

      .insert-options-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        background-color: white;
        padding: 10px;
        border-radius: 4px;
        border: 1px solid #ccc;

        .insert-label {
          font-size: 0.8rem;
          color: #666;
          margin-bottom: 5px;
        }

        .insert-btn {
          background: none;
          border: none;
          cursor: pointer;
          color: #0d3149;
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            color: #ad986f;
          }
        }
      }
    }
  }
}

.ProseMirror > :first-child.is-empty::before {
  content: "Start Typing...";
  color: #808080;
  position: absolute;
  top: 10px;
  left: 15px;
}
