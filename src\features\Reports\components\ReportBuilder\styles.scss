@use "/src/styles/mixins/mixins.scss";

.report-builder-section {
  .header {
    padding-bottom: 0.5rem;
    &-title {
      position: relative;
      display: flex;
      align-items: center;
      width: 100%;
      background: #f9f9f9;
      border: 1px solid #ccc;
      padding-right: 2.5rem;
      border-radius: 6px;

      .report-title-input {
        flex: 1;
        padding: 0.75rem 2.5rem 0.75rem 1rem;
        border: none;
        background: transparent;
        font-size: 1rem;
        outline: none;
      }

      .edit-title-btn {
        position: absolute;
        right: 0.75rem;
        background: none;
        border: none;
        cursor: pointer;
        padding: 0;
        display: flex;
        align-items: center;
        height: 100%;

        svg {
          width: 1.2rem;
          height: 1.2rem;
        }
      }
    }

    &-actions {
      .action-btn {
        background-color: #0d3149;
        border-color: #0d3149;
        color: white;
        width: 45px !important;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;

        svg {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}

.section-block {
  margin-bottom: 20px;

  .section-header {
    .lock-icon {
      margin-right: 8px;
      color: #ad986f;
    }

    .title {
      h5 {
        color: #0d3149;
        font-weight: 600;
      }
    }
  }

  .section-content {
    position: relative;

    .editor-box {
      .tiptap {
        width: 100%;
        min-height: 150px;
        padding: 0.75rem;
        border-radius: 4px;
        border: 1px solid #ccc;
        background-color: #fff;
        resize: vertical;
      }
    }

    .sidebar-container {
      position: absolute;
      right: -70px;
      top: 0;
      display: flex;
      flex-direction: column;
      gap: 20px;

      .save-btn-container {
        .save-btn {
          background-color: #0d3149;
          color: white;
          border: none;
          width: 45px;
          height: 45px;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
      }

      .insert-options-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        background-color: white;
        padding: 10px;
        border-radius: 4px;
        border: 1px solid #ccc;

        .insert-label {
          font-size: 0.8rem;
          color: #666;
          margin-bottom: 5px;
        }

        .insert-btn {
          background: none;
          border: none;
          cursor: pointer;
          color: #0d3149;
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            color: #ad986f;
          }
        }
      }
    }
  }
}

.insert-section-modal {
  .modal-dialog {
    @media only screen and (min-width: 992px) {
      width: 800px;
      max-width: 800px;
    }
  }

  .modal {
    &-body {
      .dropdown {
        .dropdown-toggle {
          padding: 0px 20px 0px 20px;
          border-radius: 5px;
          background: transparent;
          font-size: 18px;
          font-weight: bold;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 10px;
          color: #0d3149;
          border: 1px solid #0d3149;

          &:focus,
          &:active {
            box-shadow: none;
            outline: none;
          }

          &::after {
            transition: transform 0.2s ease-in-out;
          }

          &.show::after {
            transform: rotate(-180deg);
            transition: transform 0.2s ease-in-out;
          }
        }

        .dropdown-menu {
          border-radius: 10px;
          background: #ffffff;
          border: none;
          padding: 10px 0;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

          .dropdown-item {
            font-size: 16px;
            padding: 10px 20px;
            color: #0d3149;
            border-radius: 8px;

            &:hover {
              background: rgba(173, 152, 111, 0.2);
            }
          }
        }
      }
    }
  }
}
