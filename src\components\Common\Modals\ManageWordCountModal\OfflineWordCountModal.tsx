import { RiAddLine, RiCloseLine, RiSubtractLine } from "@remixicon/react";
import { useUpgradeOrgMemberWordLimit } from "api";
import { useFormik } from "formik";
import { useInvalidateQuery } from "hooks";
import { useState } from "react";
import { But<PERSON>, Form, Modal, Spinner } from "react-bootstrap";
import toast from "react-hot-toast";
import { formatNumberWithCommas } from "utils";
import * as Yup from "yup";

interface OfflineWordCountModalProps {
  show: boolean;
  onClose: () => void;
  wordCountConfig?: any;
  row?: any;
  organization?: any;
}

const ORGANISATION_MEMBERS_QUERY_KEY = "organisation-members";

export default function OfflineWordCountModal({
  show,
  onClose,
  wordCountConfig,
  row,
  organization,
}: OfflineWordCountModalProps) {
  const MIN_WORD_COUNT = 0;
  const MAX_WORD_COUNT = wordCountConfig?.word_limit;
  const STEP = 10000;

  const textContent = {
    heading: "Manage Word Count",
    subHeading: `Current usage: ${formatNumberWithCommas(wordCountConfig?.assigned_words)}/${formatNumberWithCommas(wordCountConfig?.word_limit)}`,
    label: "How much word count would you like to upgrade?",
    buttonText: "Update",
  };

  const [isLoading, setIsLoading] = useState(false);
  const { mutateAsync: upgradeOrgMemberWordLimit } =
    useUpgradeOrgMemberWordLimit();

  const [invalidateQueries] = useInvalidateQuery();

  const formik: any = useFormik({
    initialValues: { word_count: row?.word_limit },
    enableReinitialize: true,
    validationSchema: Yup.object({
      word_count: Yup.number()
        .required("Word count is required")
        .min(MIN_WORD_COUNT, `Word count must be at least ${MIN_WORD_COUNT}`)
        .max(
          MAX_WORD_COUNT,
          `Word count must be at most ${formatNumberWithCommas(MAX_WORD_COUNT)}`,
        ),
    }),
    onSubmit: async (values) => {
      try {
        setIsLoading(true);
        const result: any = await upgradeOrgMemberWordLimit({
          org_id: organization?.id,
          member_id: row?.id,
          payload: {
            word_limit: values.word_count,
          },
        });
        if (result?.success) {
          toast.success(result?.message);
          invalidateQueries([ORGANISATION_MEMBERS_QUERY_KEY]);
          onClose();
        }
      } catch (err: any) {
        console.log(err);
      } finally {
        setIsLoading(false);
      }
    },
  });

  const renderError = (field: string) => {
    if (formik.touched[field] && formik.errors[field]) {
      return <span className="mb-2 text-danger">{formik.errors[field]}</span>;
    }
  };

  const handleClose = () => {
    formik.resetForm();
    onClose();
  };

  const handleIncrement = () => {
    if (formik.values.word_count + STEP <= MAX_WORD_COUNT) {
      formik.setFieldValue("word_count", formik.values.word_count + STEP);
    }
  };

  const handleDecrement = () => {
    if (formik.values.word_count - STEP >= MIN_WORD_COUNT) {
      formik.setFieldValue("word_count", formik.values.word_count - STEP);
    }
  };

  return (
    <Modal show={show} onHide={handleClose} keyboard={false} centered>
      <Modal.Body className="d-flex justify-content-center align-items-center position-relative">
        <Button
          variant="link"
          className="text-decoration-none modal-close-button bg-brown rounded-circle position-absolute z-3 d-flex justify-content-center align-items-center"
          onClick={handleClose}
        >
          <RiCloseLine size={"40px"} color="#f9f9f9" />
        </Button>

        <div
          className="auth-form d-flex justify-content-center align-items-stretch flex-column"
          style={{ gap: "20px" }}
        >
          <div className="d-flex flex-column" style={{ gap: "15px" }}>
            <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1 fs-2">
              {textContent?.heading}
            </h1>
          </div>
          {textContent?.subHeading && (
            <div className="d-flex justify-content-center gap-3 fw-bold fs-6">
              <span>{textContent?.subHeading}</span>
            </div>
          )}

          <div className="website-form w-100">
            <Form
              className="d-flex flex-column gap-3"
              onSubmit={formik.handleSubmit}
            >
              <div className="d-flex flex-lg-row flex-column gap-3">
                <Form.Group className="m-0 p-0 position-relative w-100">
                  <Form.Label className="mb-2 w-100 text-center">
                    {textContent?.label}
                  </Form.Label>
                  <div className="position-relative">
                    <Button
                      onClick={handleDecrement}
                      style={{
                        position: "absolute",
                        left: "10px",
                        top: "50%",
                        transform: "translateY(-50%)",
                        outline: "none",
                        border: "none",
                      }}
                      variant=""
                      disabled={formik.values.word_count <= MIN_WORD_COUNT}
                    >
                      <RiSubtractLine size="24px" className="icon-left" />
                    </Button>

                    <Form.Control
                      type="number"
                      placeholder="Enter word count"
                      min={MIN_WORD_COUNT}
                      max={MAX_WORD_COUNT}
                      className="text-center fw-bold p-2"
                      {...formik.getFieldProps("word_count")}
                      style={{ paddingLeft: "40px", paddingRight: "40px" }}
                    />

                    <Button
                      onClick={handleIncrement}
                      style={{
                        position: "absolute",
                        right: "10px",
                        top: "50%",
                        transform: "translateY(-50%)",
                        outline: "none",
                        border: "none",
                      }}
                      variant=""
                      disabled={formik.values.word_count >= MAX_WORD_COUNT}
                    >
                      <RiAddLine size="24px" className="icon-right" />
                    </Button>
                  </div>
                  <div className="d-flex flex-column mt-3">
                    {renderError("word_count")}
                  </div>
                </Form.Group>
              </div>

              <div
                className="action-btns mt-1 d-flex flex-column"
                style={{ gap: "30px" }}
              >
                <Button
                  type="submit"
                  className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                  disabled={isLoading}
                >
                  {isLoading ? <Spinner /> : textContent?.buttonText}
                </Button>
              </div>
            </Form>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
}
