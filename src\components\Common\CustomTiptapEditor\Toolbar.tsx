import { useCurrentEditor } from "@tiptap/react";
import SectionFive from "./section/five";
import SectionFour from "./section/four";
import SectionOne from "./section/one";
import SectionThree from "./section/three";
import SectionTwo from "./section/two";

const Toolbar = () => {
  const editor: any = useCurrentEditor();
  <div>
    <div>
      {/* <SectionOne editor={editor} activeLevels={[1, 2, 3, 4, 5, 6]} /> */}

      <SectionTwo
        editor={editor}
        activeActions={[
          "bold",
          "italic",
          "underline",
          "strikethrough",
          "code",
          "clearFormatting",
        ]}
        mainActionCount={3}
      />

      <SectionThree editor={editor} />

      <SectionFour
        editor={editor}
        activeActions={["orderedList", "bulletList"]}
        mainActionCount={0}
      />

      <SectionFive
        editor={editor}
        activeActions={["codeBlock", "blockquote", "horizontalRule"]}
        mainActionCount={0}
      />
    </div>
  </div>;
};

export default Toolbar;
