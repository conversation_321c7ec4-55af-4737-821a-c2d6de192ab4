.editor-box {
  .tiptap {
    width: 100%;
    min-height: 150px;
    padding: 0.75rem;
    border-radius: 4px;
    border: 1px solid #ccc;
    background-color: #fff;
    resize: vertical;

    ::selection {
      background-color: #ad986f;
      color: #000;
    }

    ::-moz-selection {
      background-color: rgb(173, 152, 111);
      color: #000;
    }

    .content-locked {
      background-color: rgba(173, 152, 111, 0.15);
      border-radius: 3px;
      padding: 1px 3px;
      position: relative;

      &::before {
        content: "🔒";
        font-size: 0.7em;
        margin-right: 2px;
        opacity: 0.7;
      }

      &:hover {
        background-color: rgba(173, 152, 111, 0.25);
      }
    }

    table {
      border-collapse: collapse;
      margin: 0;
      overflow: hidden;
      table-layout: fixed;
      width: 100%;
      transition: all 0.2s ease;
      position: relative;

      &:hover {
        box-shadow: 0 2px 8px rgba(173, 152, 111, 0.15);
        border-radius: 4px;
      }

      td,
      th {
        border: 2px solid #ced4da;
        box-sizing: border-box;
        min-width: 1em;
        padding: 3px 5px;
        position: relative;
        vertical-align: top;

        > * {
          margin-bottom: 0;
        }
      }

      th {
        background-color: #f8f9fa;
        font-weight: bold;
      }

      .selectedCell:after {
        background: rgba(200, 200, 255, 0.4);
        content: "";
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        pointer-events: none;
        position: absolute;
        z-index: 2;
      }

      .column-resize-handle {
        background-color: #adf;
        bottom: -2px;
        position: absolute;
        right: -2px;
        top: 0;
        width: 4px;
      }
    }

    .graph-placeholder {
      border: 2px dashed #ad986f;
      border-radius: 8px;
      padding: 2rem;
      margin: 1rem 0;
      text-align: center;
      background-color: #f8f9fa;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background-color: #e9ecef;
        border-color: #8b7355;
      }

      .graph-placeholder-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
      }

      .graph-placeholder-icon {
        font-size: 2rem;
      }

      .graph-placeholder-text {
        font-weight: 600;
        color: #ad986f;
      }

      .graph-placeholder-subtitle {
        font-size: 0.875rem;
        color: #6c757d;
      }
    }

    img {
      max-width: 100%;
      height: auto;
      border-radius: 4px;
    }
  }
}
