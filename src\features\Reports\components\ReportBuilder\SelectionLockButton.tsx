import { RiLock2Fill, RiLockUnlockFill } from "@remixicon/react";
import { Editor } from "@tiptap/react";
import { useEffect, useState } from "react";
import { Button } from "react-bootstrap";

interface SelectionLockButtonProps {
  editor: Editor | null;
}

const SelectionLockButton = ({ editor }: SelectionLockButtonProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const [isLocked, setIsLocked] = useState(false);

  useEffect(() => {
    if (!editor) return;

    const updateButton = () => {
      const { selection } = editor.state;
      const { from, to } = selection;
      
      // Show button only when there's a text selection
      if (from !== to && !selection.empty) {
        const start = editor.view.coordsAtPos(from);
        const end = editor.view.coordsAtPos(to);
        
        // Position button above the selection
        setPosition({
          top: start.top - 50,
          left: (start.left + end.left) / 2 - 20, // Center the button
        });
        
        // Check if selection is locked
        const isSelectionLocked = editor.isActive('contentLock');
        setIsLocked(isSelectionLocked);
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    // Listen to selection changes
    const handleSelectionUpdate = () => {
      setTimeout(updateButton, 10); // Small delay to ensure DOM is updated
    };

    editor.on('selectionUpdate', handleSelectionUpdate);
    editor.on('transaction', handleSelectionUpdate);

    return () => {
      editor.off('selectionUpdate', handleSelectionUpdate);
      editor.off('transaction', handleSelectionUpdate);
    };
  }, [editor]);

  const handleLockToggle = () => {
    if (!editor) return;
    
    editor.chain().focus().toggleContentLock().run();
    
    const newLockState = editor.isActive('contentLock');
    setIsLocked(newLockState);
  };

  if (!isVisible || !editor) {
    return null;
  }

  return (
    <div
      className="selection-lock-button"
      style={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        zIndex: 1000,
      }}
    >
      <Button
        variant=""
        className={`lock-btn ${isLocked ? 'locked' : 'unlocked'}`}
        onClick={handleLockToggle}
        title={isLocked ? 'Unlock content' : 'Lock content'}
      >
        {isLocked ? (
          <RiLock2Fill size={16} />
        ) : (
          <RiLockUnlockFill size={16} />
        )}
      </Button>
    </div>
  );
};

export default SelectionLockButton;
