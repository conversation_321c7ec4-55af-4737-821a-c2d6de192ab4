import { create } from "zustand";

interface ReportBlock {
  title: string;
  content: string;
  sortOrder: number;
}

interface PayloadInterface {
  reportInfo: {
    title: string;
    blocks: ReportBlock[];
  };
}

const initialState = {
  reportInfo: {
    title: "",
    blocks: [
      {
        sortOrder: 0,
        title: "Introduction",
        content: "At our recent review meeting...",
      },
    ],
  },
};

const reportStore = (set: any) => ({
  ...initialState,
  setReportTitle: (data: string) =>
    set((state: PayloadInterface) => ({
      ...state,
      reportInfo: { ...state.reportInfo, title: data },
    })),
  setReportBlocks: (data: ReportBlock) =>
    set((state: PayloadInterface) => ({
      ...state,
      reportInfo: {
        ...state.reportInfo,
        blocks: [...state.reportInfo.blocks, data],
      },
    })),
  resetReportState: () => set(() => initialState),
});

// const useReportStore: any = create(
//   devtools(
//     persist(reportStore, {
//       name: "report",
//     })
//   )
// );

const useReportStore = create(reportStore);

export const { setReportTitle, setReportBlocks, resetReportState } =
  useReportStore.getState();

export default useReportStore;
