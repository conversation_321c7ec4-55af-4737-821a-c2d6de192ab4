import {
    Ri<PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    RiE<PERSON>2<PERSON>ill,
    RiGroup3Fill,
    RiSave2Fill,
} from "@remixicon/react";
import { setReportTitle } from "features/Reports/store";
import { useEffect, useRef, useState } from "react";
import ActionButton from "../ActionButton";

interface HeaderBarProps {
  title?: string;
}

const HeaderBar = ({ title = "" }: HeaderBarProps) => {
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const titleRef = useRef<HTMLInputElement>(null);

  const handleSaveTitle = () => {
    if (titleRef.current) {
      titleRef.current.blur();
      setReportTitle(titleRef.current.value);
      setIsEditingTitle(false);
    }
  };

  const handleEditTitle = () => {
    setIsEditingTitle(true);
    if (titleRef.current) {
      titleRef.current.focus();
    }
  };

  const handleCheckCompliance = () => {};

  const handleShowPreview = () => {};

  const handleSaveReport = () => {};

  useEffect(() => {
    if (titleRef.current) {
      titleRef.current.value = title || "";
    }
  }, [title]);
  return (
    <div className="header d-flex gap-3">
      <div className="header-title rounded w-100 position-relative">
        <input
          type="text"
          className="report-title-input w-100 text-center fw-bold"
          placeholder="Enter report title..."
          readOnly={!isEditingTitle}
          ref={titleRef}
        />
        {isEditingTitle ? (
          <button className="edit-title-btn" onClick={handleSaveTitle}>
            <RiCheckFill />
          </button>
        ) : (
          <button className="edit-title-btn" onClick={handleEditTitle}>
            <RiEdit2Fill />
          </button>
        )}
      </div>
      <div className="header-actions d-flex gap-2">
        <ActionButton icon={RiArticleFill} onClick={handleCheckCompliance} />
        <ActionButton icon={RiGroup3Fill} onClick={handleShowPreview} />
        <ActionButton icon={RiSave2Fill} onClick={handleSaveReport} />
      </div>
    </div>
  );
};

export default HeaderBar;
