import {
    Ri<PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    Ri<PERSON><PERSON>2<PERSON>ill,
    Ri<PERSON><PERSON><PERSON><PERSON>ill,
    RiSave2Fill,
} from "@remixicon/react";
import { setReportTitle } from "features/Reports/store";
import useReportStore from "features/Reports/store/report";
import { useEffect, useRef, useState } from "react";
import ActionButton from "../ActionButton";
import ReportPreview from "./ReportPreview";

interface HeaderBarProps {
  title?: string;
}

const HeaderBar = ({ title = "" }: HeaderBarProps) => {
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const titleRef = useRef<HTMLInputElement>(null);
  const reportInfo = useReportStore((state) => state.reportInfo);

  const handleSaveTitle = () => {
    if (titleRef.current) {
      titleRef.current.blur();
      setReportTitle(titleRef.current.value);
      setIsEditingTitle(false);
    }
  };

  const handleEditTitle = () => {
    setIsEditingTitle(true);
    if (titleRef.current) {
      titleRef.current.focus();
    }
  };

  const handleCheckCompliance = () => {
    console.log("Check compliance clicked");
  };

  const handleShowPreview = () => {
    setShowPreview(true);
  };

  const handleSaveReport = () => {
    console.log("Report saved:", reportInfo);
  };

  useEffect(() => {
    if (titleRef.current) {
      titleRef.current.value = title || "";
    }
  }, [title]);
  return (
    <>
      <div className="header d-flex gap-3">
        <div className="header-title rounded w-100 position-relative">
          <input
            type="text"
            className="report-title-input w-100 text-center fw-bold"
            placeholder="Enter report title..."
            readOnly={!isEditingTitle}
            ref={titleRef}
          />
          {isEditingTitle ? (
            <button className="edit-title-btn" onClick={handleSaveTitle}>
              <RiCheckFill />
            </button>
          ) : (
            <button className="edit-title-btn" onClick={handleEditTitle}>
              <RiEdit2Fill />
            </button>
          )}
        </div>
        <div className="header-actions d-flex gap-2">
          <ActionButton icon={RiArticleFill} onClick={handleCheckCompliance} />
          <ActionButton icon={RiGroup3Fill} onClick={handleShowPreview} />
          <ActionButton icon={RiSave2Fill} onClick={handleSaveReport} />
        </div>
      </div>

      {showPreview && (
        <ReportPreview
          show={showPreview}
          onClose={() => setShowPreview(false)}
        />
      )}
    </>
  );
};

export default HeaderBar;
