import { Node, mergeAttributes } from '@tiptap/core';

export interface GraphPlaceholderOptions {
  HTMLAttributes: Record<string, any>;
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    graphPlaceholder: {
      /**
       * Insert a graph placeholder
       */
      insertGraphPlaceholder: () => ReturnType;
    };
  }
}

export const GraphPlaceholder = Node.create<GraphPlaceholderOptions>({
  name: 'graphPlaceholder',

  addOptions() {
    return {
      HTMLAttributes: {},
    };
  },

  group: 'block',

  atom: true,

  addAttributes() {
    return {
      id: {
        default: null,
      },
      title: {
        default: 'Graph Placeholder',
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="graph-placeholder"]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'div',
      mergeAttributes(
        {
          'data-type': 'graph-placeholder',
          class: 'graph-placeholder',
        },
        this.options.HTMLAttributes,
        HTMLAttributes
      ),
      [
        'div',
        { class: 'graph-placeholder-content' },
        [
          'div',
          { class: 'graph-placeholder-icon' },
          '📊'
        ],
        [
          'div',
          { class: 'graph-placeholder-text' },
          HTMLAttributes.title || 'Graph Placeholder'
        ],
        [
          'div',
          { class: 'graph-placeholder-subtitle' },
          'Click to add a graph'
        ]
      ]
    ];
  },

  addCommands() {
    return {
      insertGraphPlaceholder:
        () =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: {
              id: `graph-${Date.now()}`,
              title: 'Graph Placeholder',
            },
          });
        },
    };
  },
});

export default GraphPlaceholder;
