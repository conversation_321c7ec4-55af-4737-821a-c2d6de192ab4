import { EditorProvider } from "@tiptap/react";
import "./styles.scss";
import Toolbar from "./Toolbar";

const CustomTiptapEditor = ({ content, extensions }: any) => {
  return (
    <EditorProvider
      slotBefore={<Toolbar />}
      extensions={extensions}
      content={content}
      editorContainerProps={{
        className: "editor-box w-100",
      }}
    ></EditorProvider>
  );
};

export default CustomTiptapEditor;
