import { Button } from "react-bootstrap";

interface ActionButtonProps {
  icon: any;
  onClick?: () => void;
}

const ActionButton = ({ icon, onClick = () => {} }: ActionButtonProps) => {
  const Icon = icon;
  return (
    <Button
      onClick={onClick}
      variant=""
      className="w-100 py-2 bg-blue font-light border-blue text-decoration-none fw-bold d-flex justify-content-between align-items-center"
    >
      <Icon />
    </Button>
  );
};

export default ActionButton;
