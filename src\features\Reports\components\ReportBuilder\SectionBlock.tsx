import {
  RiAddLargeLine,
  Ri<PERSON>rrowUpDownLine,
  RiCloseLargeLine,
  RiLock2Fill,
} from "@remixicon/react";
import Highlight from "@tiptap/extension-highlight";
import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { useState } from "react";
import { Button } from "react-bootstrap";
import InsertSectionModal from "./InsertSectionModal";

interface SectionBlockProps {
  title: string;
  content: string;
}

const extensions = [StarterKit, Highlight];

const SectionBlock = ({ title, content }: SectionBlockProps) => {
  const [showInsertModal, setShowInsertModal] = useState(false);

  const editor = useEditor({
    extensions,
    content,
  });
  return (
    <>
      <div className="section-block mb-4 p-3 rounded">
        <div className="section-header d-flex justify-content-between align-items-center mb-2">
          <div className="title d-flex align-items-center gap-3">
            <RiLock2Fill color="#ad986f" />
            <h5 className="mb-0 fw-bold">{title}</h5>
          </div>
          <div className="controls">
            <Button variant="" onClick={() => setShowInsertModal(true)}>
              <RiAddLargeLine color="#ad986f" />
            </Button>
            <Button variant="">
              <RiArrowUpDownLine color="#ad986f" />
            </Button>
            <Button variant="">
              <RiCloseLargeLine color="#ad986f" />
            </Button>
          </div>
        </div>
        <div className="section-content">
          <EditorContent editor={editor} className="editor-box" />
        </div>
      </div>
      {showInsertModal && (
        <InsertSectionModal
          show={showInsertModal}
          onClose={() => setShowInsertModal(false)}
        />
      )}
    </>
  );
};

export default SectionBlock;
