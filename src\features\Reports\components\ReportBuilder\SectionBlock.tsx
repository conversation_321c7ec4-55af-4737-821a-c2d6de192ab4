import {
  RiAddLargeLine,
  RiArrowUpDownLine,
  RiCloseLargeLine,
  RiLock2Fill,
} from "@remixicon/react";
import Highlight from "@tiptap/extension-highlight";
import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { useState } from "react";
import { Button } from "react-bootstrap";
import { removeReportBlockAtIndex, swapReportBlocks } from "features/Reports/store";
import useReportStore from "features/Reports/store/report";
import InsertSectionModal from "./InsertSectionModal";

interface SectionBlockProps {
  title: string;
  content: string;
  index: number;
}

const extensions = [StarterKit, Highlight];

const SectionBlock = ({ title, content, index }: SectionBlockProps) => {
  const [showInsertModal, setShowInsertModal] = useState(false);
  const reportInfo = useReportStore((state) => state.reportInfo);

  const editor = useEditor({
    extensions,
    content,
  });

  const handleRemoveSection = () => {
    // Prevent removing the last section
    if (reportInfo.blocks.length > 1) {
      removeReportBlockAtIndex(index);
    }
  };

  const handleReorderSection = () => {
    const nextIndex = index + 1;
    const prevIndex = index - 1;

    // Try to swap with next section first, then with previous section
    if (nextIndex < reportInfo.blocks.length) {
      swapReportBlocks(index, nextIndex);
    } else if (prevIndex >= 0) {
      swapReportBlocks(index, prevIndex);
    }
  };
  return (
    <>
      <div className="section-block mb-4 p-3 rounded">
        <div className="section-header d-flex justify-content-between align-items-center mb-2">
          <div className="title d-flex align-items-center gap-3">
            <RiLock2Fill color="#ad986f" />
            <h5 className="mb-0 fw-bold">{title}</h5>
          </div>
          <div className="controls">
            <Button variant="" onClick={() => setShowInsertModal(true)}>
              <RiAddLargeLine color="#ad986f" />
            </Button>
            <Button variant="" onClick={handleReorderSection}>
              <RiArrowUpDownLine color="#ad986f" />
            </Button>
            <Button variant="" onClick={handleRemoveSection}>
              <RiCloseLargeLine color="#ad986f" />
            </Button>
          </div>
        </div>
        <div className="section-content">
          <EditorContent editor={editor} className="editor-box" />
        </div>
      </div>
      {showInsertModal && (
        <InsertSectionModal
          show={showInsertModal}
          onClose={() => setShowInsertModal(false)}
          index={index}
        />
      )}
    </>
  );
};

export default SectionBlock;
