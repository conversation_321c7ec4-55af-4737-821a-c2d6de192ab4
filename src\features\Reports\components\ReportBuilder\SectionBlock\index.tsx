import {
  Ri<PERSON>dd<PERSON>argeLine,
  Ri<PERSON>rrowUpDownLine,
  RiCloseLargeLine,
  RiLock2Fill,
} from "@remixicon/react";
import Highlight from "@tiptap/extension-highlight";
import Image from "@tiptap/extension-image";
import Placeholder from "@tiptap/extension-placeholder";
import Table from "@tiptap/extension-table";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import TableRow from "@tiptap/extension-table-row";
import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import {
  removeReportBlockAtIndex,
  swapReportBlocks,
  updateReportBlockTitle
} from "features/Reports/store";
import useReportStore from "features/Reports/store/report";
import { useEffect, useRef, useState } from "react";
import { Button } from "react-bootstrap";
import EditorToolbar from "../EditorToolbar";
import GraphPlaceholder from "../extensions/GraphPlaceholder";
import InsertSectionModal from "../InsertSectionModal";
import "./styles.scss";

interface SectionBlockProps {
  title: string;
  content: string;
  index: number;
}

const extensions = [
  StarterKit,
  Highlight,
  Table.configure({
    resizable: true,
  }),
  TableRow,
  TableHeader,
  TableCell,
  Image.configure({
    inline: true,
    allowBase64: true,
  }),
  GraphPlaceholder,
  Placeholder.configure({
    placeholder: "Start writing here...",
    emptyEditorClass: "is-editor-empty",
    showOnlyWhenEditable: true,
    showOnlyCurrent: false,
  }),
];

const SectionBlock = ({ title, content, index }: SectionBlockProps) => {
  const [showInsertModal, setShowInsertModal] = useState(false);
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [localTitle, setLocalTitle] = useState(title);
  const reportInfo = useReportStore((state) => state.reportInfo);
  const titleRef = useRef<HTMLInputElement>(null);

  const editor = useEditor({
    extensions,
    content,
  });

  useEffect(() => {
    setLocalTitle(title);
  }, [title]);

  const handleTitleClick = () => {
    setIsEditingTitle(true);
    setTimeout(() => {
      if (titleRef.current) {
        titleRef.current.focus();
        titleRef.current.select();
      }
    }, 0);
  };

  const handleTitleSave = () => {
    if (titleRef.current) {
      const newTitle = titleRef.current.value.trim();
      if (newTitle && newTitle !== title) {
        updateReportBlockTitle(index, newTitle);
        setLocalTitle(newTitle);
      } else if (!newTitle) {
        setLocalTitle(title);
      }
      setIsEditingTitle(false);
    }
  };

  const handleTitleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleTitleSave();
    } else if (e.key === "Escape") {
      setLocalTitle(title);
      setIsEditingTitle(false);
    }
  };

  const handleTitleBlur = () => {
    handleTitleSave();
  };

  const handleSaveContent = () => {
    if (editor) {
      const content = editor.getJSON();
      // updateReportBlockContent(index, content);
      console.log(content);
    }
  };

  const handleRemoveSection = () => {
    if (reportInfo.blocks.length > 1) {
      removeReportBlockAtIndex(index);
    }
  };

  const handleReorderSection = () => {
    const nextIndex = index + 1;
    const prevIndex = index - 1;

    if (nextIndex < reportInfo.blocks.length) {
      swapReportBlocks(index, nextIndex);
    } else if (prevIndex >= 0) {
      swapReportBlocks(index, prevIndex);
    }
  };
  return (
    <>
      <div className="section-block mb-4 p-3 rounded">
        <div className="section-header d-flex justify-content-between align-items-center mb-2">
          <div className="title d-flex align-items-center gap-3">
            <RiLock2Fill color="#ad986f" />
            {isEditingTitle ? (
              <input
                ref={titleRef}
                type="text"
                className="section-title-input fw-bold"
                defaultValue={localTitle}
                onKeyDown={handleTitleKeyDown}
                onBlur={handleTitleBlur}
                autoFocus
              />
            ) : (
              <h5
                className="mb-0 fw-bold section-title-display"
                onClick={handleTitleClick}
                title="Click to edit title"
              >
                {localTitle}
              </h5>
            )}
          </div>
          <div className="controls">
            <Button variant="" onClick={() => setShowInsertModal(true)}>
              <RiAddLargeLine color="#ad986f" />
            </Button>
            <Button variant="" onClick={handleReorderSection}>
              <RiArrowUpDownLine color="#ad986f" />
            </Button>
            <Button variant="" onClick={handleRemoveSection}>
              <RiCloseLargeLine color="#ad986f" />
            </Button>
          </div>
        </div>
        <div className="section-content">
          <div className="editor-container d-flex gap-3">
            <EditorContent editor={editor} className="editor-box w-100" />
            <EditorToolbar editor={editor} onSave={handleSaveContent} />
          </div>
        </div>
      </div>
      {showInsertModal && (
        <InsertSectionModal
          show={showInsertModal}
          onClose={() => setShowInsertModal(false)}
          index={index}
        />
      )}
    </>
  );
};

export default SectionBlock;
